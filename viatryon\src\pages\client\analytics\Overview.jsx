import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  <PERSON><PERSON><PERSON>,
  Line,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer
} from 'recharts';
import { Eye, Users, Clock, TrendingUp } from 'lucide-react';

const Overview = ({ timeRange }) => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [overviewData, setOverviewData] = useState(null);

  useEffect(() => {
    const fetchOverviewData = async () => {
      try {
        setLoading(true);
        setError(null);

        const token = localStorage.getItem('token');
        if (!token) {
          throw new Error('No authentication token found');
        }

        const baseUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';
        const apiUrl = baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl;
        
        // Calculate date range based on timeRange
        const end = new Date();
        let start = new Date();
        switch (timeRange) {
          case '7d':
            start.setDate(start.getDate() - 7);
            break;
          case '30d':
            start.setDate(start.getDate() - 30);
            break;
          case '90d':
            start.setDate(start.getDate() - 90);
            break;
          case '1y':
            start.setFullYear(start.getFullYear() - 1);
            break;
          default:
            start.setDate(start.getDate() - 7);
        }

        // Fetch overview data
        const overviewResponse = await fetch(`${apiUrl}/api/analytics/client/overview?start=${start.toISOString()}&end=${end.toISOString()}`, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        });

        if (!overviewResponse.ok) {
          const errorData = await overviewResponse.json().catch(() => ({}));
          throw new Error(errorData.message || `Failed to fetch overview data: ${overviewResponse.status}`);
        }

        const data = await overviewResponse.json();
        setOverviewData(data);
      } catch (err) {
        console.error('Error fetching overview data:', err);
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    fetchOverviewData();
  }, [timeRange]);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#2D8C88]"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-4">
        <div className="flex">
          <div className="ml-3">
            <h3 className="text-sm font-medium text-red-800">Error loading overview</h3>
            <div className="mt-2 text-sm text-red-700">
              <p>{error}</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Format duration from seconds to readable format
  const formatDuration = (seconds) => {
    if (!seconds) return '0s';
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return minutes > 0 ? `${minutes}m ${remainingSeconds}s` : `${remainingSeconds}s`;
  };

  // Format numbers with commas
  const formatNumber = (num) => {
    if (!num) return '0';
    return num.toLocaleString();
  };

  // Calculate trend changes from previous period
  const calculateTrendChange = (current, previous) => {
    if (!previous) return { value: '0%', trend: 'neutral' };
    const change = ((current - previous) / previous) * 100;
    return {
      value: `${change >= 0 ? '+' : ''}${change.toFixed(1)}%`,
      trend: change > 0 ? 'up' : change < 0 ? 'down' : 'neutral'
    };
  };

  const metrics = [
    {
      title: 'Total Try-Ons',
      value: formatNumber(overviewData?.totalSessions || 0),
      change: calculateTrendChange(
        overviewData?.totalSessions || 0,
        overviewData?.previousPeriod?.totalSessions || 0
      ).value,
      trend: calculateTrendChange(
        overviewData?.totalSessions || 0,
        overviewData?.previousPeriod?.totalSessions || 0
      ).trend,
      icon: <Eye className="h-6 w-6" />
    },
    {
      title: 'Average Duration',
      value: formatDuration(Math.round(overviewData?.avgDuration || 0)),
      change: calculateTrendChange(
        overviewData?.avgDuration || 0,
        overviewData?.previousPeriod?.avgDuration || 0
      ).value,
      trend: calculateTrendChange(
        overviewData?.avgDuration || 0,
        overviewData?.previousPeriod?.avgDuration || 0
      ).trend,
      icon: <Clock className="h-6 w-6" />
    },
    {
      title: 'Total Interactions',
      value: formatNumber(overviewData?.totalInteractions || 0),
      change: calculateTrendChange(
        overviewData?.totalInteractions || 0,
        overviewData?.previousPeriod?.totalInteractions || 0
      ).value,
      trend: calculateTrendChange(
        overviewData?.totalInteractions || 0,
        overviewData?.previousPeriod?.totalInteractions || 0
      ).trend,
      icon: <TrendingUp className="h-6 w-6" />
    },
    {
      title: 'Unique Users',
      value: formatNumber(overviewData?.uniqueUsers || 0),
      change: calculateTrendChange(
        overviewData?.uniqueUsers || 0,
        overviewData?.previousPeriod?.uniqueUsers || 0
      ).value,
      trend: calculateTrendChange(
        overviewData?.uniqueUsers || 0,
        overviewData?.previousPeriod?.uniqueUsers || 0
      ).trend,
      icon: <Users className="h-6 w-6" />
    }
  ];

  return (
    <div className="space-y-6">
      {/* Key Metrics */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
        {metrics.map((metric, index) => (
          <motion.div
            key={metric.title}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
            className="bg-white rounded-xl shadow-sm p-6"
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">{metric.title}</p>
                <p className="text-2xl font-semibold text-gray-900 mt-1">{metric.value}</p>
              </div>
              <div className="w-12 h-12 rounded-full bg-[#2D8C88]/10 flex items-center justify-center">
                {metric.icon}
              </div>
            </div>
            <div className="mt-4">
              <span className={`text-sm font-medium ${
                metric.trend === 'up' ? 'text-green-600' : 'text-red-600'
              }`}>
                {metric.change}
              </span>
              <span className="text-sm text-gray-600 ml-2">from last period</span>
            </div>
          </motion.div>
        ))}
      </div>

      {/* Charts Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
        {/* Try-On Trends */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
          className="bg-white rounded-xl shadow-sm p-6"
        >
          <h3 className="text-lg font-medium text-gray-900 mb-4">Try-On Trends</h3>
          <div className="h-64 md:h-80">
            <ResponsiveContainer width="100%" height="100%">
              <LineChart data={overviewData?.trends || []}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="date" />
                <YAxis />
                <Tooltip />
                <Line
                  type="monotone"
                  dataKey="sessions"
                  stroke="#2D8C88"
                  strokeWidth={2}
                  dot={{ fill: '#2D8C88' }}
                />
              </LineChart>
            </ResponsiveContainer>
          </div>
        </motion.div>

        {/* Device Distribution */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
          className="bg-white rounded-xl shadow-sm p-6"
        >
          <h3 className="text-lg font-medium text-gray-900 mb-4">Device Distribution</h3>
          <div className="h-64 md:h-80">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={overviewData?.devices || []}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="_id" />
                <YAxis />
                <Tooltip />
                <Bar dataKey="sessions" fill="#2D8C88" />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </motion.div>
      </div>

      {/* Recent Activity */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.6 }}
        className="bg-white rounded-xl shadow-sm p-6"
      >
        <h3 className="text-lg font-medium text-gray-900 mb-4">Recent Activity</h3>
        <div className="space-y-4">
          {overviewData?.recentActivity?.map((activity, index) => (
            <div key={index} className="flex items-start space-x-4">
              <div className={`w-10 h-10 rounded-full flex items-center justify-center ${
                activity.type === 'tryon' ? 'bg-[#2D8C88]/10' :
                activity.type === 'conversion' ? 'bg-green-500/10' :
                'bg-blue-500/10'
              }`}>
                {activity.type === 'tryon' ? <Eye className="h-5 w-5 text-[#2D8C88]" /> :
                 activity.type === 'conversion' ? <TrendingUp className="h-5 w-5 text-green-500" /> :
                 <Users className="h-5 w-5 text-blue-500" />}
              </div>
              <div className="flex-1">
                <p className="text-sm font-medium text-gray-900">{activity.title}</p>
                <p className="text-sm text-gray-600">{activity.description}</p>
                <p className="text-xs text-gray-500 mt-1">{activity.timeAgo}</p>
              </div>
            </div>
          ))}
          {(!overviewData?.recentActivity || overviewData.recentActivity.length === 0) && (
            <p className="text-sm text-gray-500 text-center py-4">No recent activity</p>
          )}
        </div>
      </motion.div>
    </div>
  );
};

export default Overview; 