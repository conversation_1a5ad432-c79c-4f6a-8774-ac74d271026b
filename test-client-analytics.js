// Using built-in fetch (Node.js 18+)

async function testClientAnalytics() {
  try {
    // First, let's try to login with a test client
    console.log('Attempting to login...');
    const loginResponse = await fetch('http://localhost:5000/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'client123'
      })
    });

    console.log('Login response status:', loginResponse.status);
    const loginData = await loginResponse.json();
    console.log('Login response:', loginData);

    if (loginResponse.ok && loginData.token) {
      const token = loginData.token;

      // Test the auth/me endpoint
      console.log('\nTesting auth/me endpoint...');
      const authResponse = await fetch('http://localhost:5000/api/auth/me', {
        headers: {
          'Authorization': `Bear<PERSON> ${token}`,
          'Content-Type': 'application/json'
        }
      });

      console.log('Auth response status:', authResponse.status);
      const authData = await authResponse.json();
      console.log('Auth response:', authData);

      // Test the client analytics endpoint
      console.log('\nTesting client analytics endpoint...');
      const analyticsResponse = await fetch('http://localhost:5000/api/analytics/client/overview', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      console.log('Analytics response status:', analyticsResponse.status);
      const analyticsData = await analyticsResponse.json();
      console.log('Analytics response:', analyticsData);
    } else {
      console.log('Login failed, cannot test analytics');
    }

  } catch (error) {
    console.error('Test error:', error);
  }
}

testClientAnalytics();
