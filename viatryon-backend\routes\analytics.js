const express = require('express');
const router = express.Router();
const { body, validationResult } = require('express-validator');
const TryOnSession = require('../models/TryOnSession');
const Product = require('../models/Product');
const User = require('../models/User');

// Middleware to verify JWT token and admin role
const auth = async (req, res, next) => {
  try {
    const token = req.header('Authorization')?.replace('Bearer ', '');
    console.log('Auth middleware - token present:', !!token);

    if (!token) {
      console.log('Auth middleware - no token provided');
      return res.status(401).json({ message: 'No authentication token, access denied' });
    }

    const jwt = require('jsonwebtoken');
    const verified = jwt.verify(token, process.env.JWT_SECRET);
    console.log('Auth middleware - token verified:', verified);
    req.user = verified;
    next();
  } catch (err) {
    console.log('Auth middleware - token verification failed:', err.message);
    res.status(401).json({ message: 'Token verification failed, authorization denied' });
  }
};

// Middleware to check admin role
const requireAdmin = (req, res, next) => {
  if (req.user.role !== 'admin') {
    return res.status(403).json({ message: 'Admin access required' });
  }
  next();
};

// Middleware to check client access
const requireClientAccess = async (req, res, next) => {
  console.log('RequireClientAccess middleware - user role:', req.user?.role);

  if (req.user.role === 'admin') {
    console.log('RequireClientAccess - admin access granted');
    return next(); // Admins can access all data
  }

  if (req.user.role === 'client') {
    req.clientId = req.user.id; // Clients can only access their own data
    console.log('RequireClientAccess - client access granted, clientId set to:', req.clientId);
    return next();
  }

  console.log('RequireClientAccess - access denied for role:', req.user?.role);
  return res.status(403).json({ message: 'Access denied' });
};

// Helper function to parse date range
const parseDateRange = (req) => {
  const { startDate, endDate, start: startParam, end: endParam, period } = req.query;
  let start, end;

  // Accept both startDate/endDate and start/end parameter names
  if ((startDate && endDate) || (startParam && endParam)) {
    start = new Date(startDate || startParam);
    end = new Date(endDate || endParam);
  } else if (period) {
    end = new Date();
    switch (period) {
      case '7d':
        start = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
        break;
      case '30d':
        start = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
        break;
      case '90d':
        start = new Date(Date.now() - 90 * 24 * 60 * 60 * 1000);
        break;
      default:
        start = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
    }
  } else {
    // Default to last 30 days
    end = new Date();
    start = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
  }

  return { start, end };
};

// Helper function to safely convert to ObjectId
const toObjectId = (id) => {
  console.log('toObjectId called with:', id, 'type:', typeof id, 'constructor:', id?.constructor?.name);

  if (!id) {
    console.log('toObjectId: id is null/undefined');
    return null;
  }

  // If it's already an ObjectId, return it
  if (id && typeof id === 'object' && id.constructor.name === 'ObjectId') {
    console.log('toObjectId: already ObjectId');
    return id;
  }

  // If it's a string that looks like an ObjectId, convert it
  if (typeof id === 'string' && /^[0-9a-fA-F]{24}$/.test(id)) {
    try {
      const objectId = require('mongoose').Types.ObjectId(id);
      console.log('toObjectId: converted string to ObjectId:', objectId);
      return objectId;
    } catch (err) {
      console.log('toObjectId: conversion failed:', err.message);
      return null;
    }
  }

  console.log('toObjectId: invalid format');
  return null;
};

// POST /api/analytics/session - Create a new try-on session (public endpoint)
router.post('/session', async (req, res) => {
  try {
    // Generate unique session ID
    const sessionId = require('crypto').randomBytes(16).toString('hex');

    // Handle client ID - if it's not a valid ObjectId, store it as a string
    const clientId = req.body.clientId;
    let clientIdToStore = clientId;
    
    // Try to convert to ObjectId if it looks like one
    if (clientId && /^[0-9a-fA-F]{24}$/.test(clientId)) {
      try {
        clientIdToStore = require('mongoose').Types.ObjectId(clientId);
      } catch (err) {
        // If conversion fails, keep it as a string
        console.warn('Invalid ObjectId format for clientId:', clientId);
      }
    }

    const sessionData = {
      ...req.body,
      sessionId,
      // userId is optional for anonymous sessions
      userId: req.body.userId || null,
      clientId: clientIdToStore,
      startTime: new Date(),
      // Extract device info from user agent
      device: {
        type: req.body.device?.type || detectDeviceType(req.headers['user-agent']),
        os: req.body.device?.os || null,
        browser: req.body.device?.browser || null,
        screenResolution: req.body.device?.screenResolution || null
      },
      // Extract location info (you might want to use a GeoIP service)
      location: {
        country: req.body.location?.country || null,
        region: req.body.location?.region || null,
        city: req.body.location?.city || null,
        timezone: req.body.location?.timezone || null,
        ip: req.body.location?.userIP || req.ip || req.headers['x-forwarded-for'] || req.headers['x-real-ip'] || '127.0.0.1',
        userIP: req.body.location?.userIP || req.ip || req.headers['x-forwarded-for'] || req.headers['x-real-ip'] || '127.0.0.1',
        referrer: req.headers.referer || req.body.referrer || req.body.location?.referrer
      },
      userAgent: req.headers['user-agent'],
      referrer: req.headers.referer || req.body.referrer
    };

    const session = new TryOnSession(sessionData);
    await session.save();

    // Update product analytics if product exists
    if (sessionData.productId) {
      await Product.findOneAndUpdate(
        { productId: sessionData.productId },
        { $inc: { 'analytics.totalTryOns': 1 } }
      );
    }

    res.status(201).json({
      message: 'Session recorded',
      sessionId: session.sessionId
    });
  } catch (error) {
    console.error('Session recording error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Helper function to detect device type from user agent
function detectDeviceType(userAgent) {
  if (!userAgent) return 'unknown';

  const ua = userAgent.toLowerCase();
  if (ua.includes('mobile')) {
    return 'mobile';
  } else if (ua.includes('tablet') || ua.includes('ipad')) {
    return 'tablet';
  } else {
    return 'desktop';
  }
}

// Admin Analytics Routes

// GET /api/analytics/admin/overview - Admin overview analytics
router.get('/admin/overview', auth, requireAdmin, async (req, res) => {
  try {
    const { start, end } = parseDateRange(req);
    
    // Calculate previous period dates
    const periodDuration = end - start;
    const previousStart = new Date(start.getTime() - periodDuration);
    const previousEnd = new Date(start);
    
    // Get current period statistics
    const totalClients = await User.countDocuments({ role: 'client' });
    const activeClients = await User.countDocuments({ 
      role: 'client', 
      lastLoginAt: { $gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) }
    });
    
    const totalSessions = await TryOnSession.countDocuments({
      createdAt: { $gte: start, $lte: end }
    });

    // Get previous period statistics
    const previousTotalSessions = await TryOnSession.countDocuments({
      createdAt: { $gte: previousStart, $lte: previousEnd }
    });
    
    // Calculate growth metrics
    const tryOnsGrowth = previousTotalSessions > 0 ? ((totalSessions - previousTotalSessions) / previousTotalSessions) * 100 : 0;
    
    // Get trends data - calculate duration from actual fields
    const trendsData = await TryOnSession.aggregate([
      {
        $match: {
          createdAt: { $gte: start, $lte: end }
        }
      },
      {
        $addFields: {
          calculatedDuration: {
            $cond: [
              { $and: [{ $ne: ['$startTime', null] }, { $ne: ['$updatedAt', null] }] },
              { $divide: [{ $subtract: ['$updatedAt', '$startTime'] }, 1000] },
              0
            ]
          }
        }
      },
      {
        $group: {
          _id: {
            $dateToString: { format: "%Y-%m-%d", date: "$createdAt" }
          },
          tryOns: { $sum: 1 },
          avgDuration: { $avg: '$calculatedDuration' }
        }
      },
      { $sort: { _id: 1 } }
    ]);

    // Calculate average session duration from actual fields
    const durationStats = await TryOnSession.aggregate([
      {
        $match: {
          createdAt: { $gte: start, $lte: end },
          startTime: { $exists: true, $ne: null },
          updatedAt: { $exists: true, $ne: null }
        }
      },
      {
        $addFields: {
          calculatedDuration: {
            $divide: [{ $subtract: ['$updatedAt', '$startTime'] }, 1000]
          }
        }
      },
      {
        $group: {
          _id: null,
          avgDuration: { $avg: '$calculatedDuration' },
          totalDuration: { $sum: '$calculatedDuration' }
        }
      }
    ]);

    const avgSessionDuration = durationStats[0]?.avgDuration || 0;
    const totalDuration = durationStats[0]?.totalDuration || 0;

    // Calculate total interactions - only count if interactions array exists and has data
    const interactionStats = await TryOnSession.aggregate([
      {
        $match: {
          createdAt: { $gte: start, $lte: end },
          interactions: { $exists: true, $ne: [] }
        }
      },
      {
        $group: {
          _id: null,
          totalInteractions: { $sum: { $size: '$interactions' } }
        }
      }
    ]);

    const totalInteractions = interactionStats[0]?.totalInteractions || 0;

    // Calculate duration growth from actual fields
    const previousDurationStats = await TryOnSession.aggregate([
      {
        $match: {
          createdAt: { $gte: previousStart, $lte: previousEnd },
          startTime: { $exists: true, $ne: null },
          updatedAt: { $exists: true, $ne: null }
        }
      },
      {
        $addFields: {
          calculatedDuration: {
            $divide: [{ $subtract: ['$updatedAt', '$startTime'] }, 1000]
          }
        }
      },
      {
        $group: {
          _id: null,
          avgDuration: { $avg: '$calculatedDuration' }
        }
      }
    ]);

    const previousAvgDuration = previousDurationStats[0]?.avgDuration || 0;
    const durationGrowth = previousAvgDuration > 0 ?
      ((avgSessionDuration - previousAvgDuration) / previousAvgDuration) * 100 : 0;

    res.json({
      totalSessions,
      totalClients,
      activeClients,
      totalInteractions,
      tryOnsGrowth: Math.round(tryOnsGrowth * 100) / 100,
      avgSessionDuration: Math.round(avgSessionDuration || 0),
      totalDuration: Math.round(totalDuration || 0),
      durationGrowth: Math.round(durationGrowth * 100) / 100,
      trends: trendsData.map(trend => ({
        date: trend._id,
        tryOns: trend.tryOns,
        avgDuration: Math.round(trend.avgDuration || 0)
      }))
    });
  } catch (error) {
    console.error('Error fetching overview analytics:', error);
    res.status(500).json({ message: 'Error fetching overview analytics' });
  }
});

// GET /api/analytics/admin/unique-users - Get unique users by IP
router.get('/admin/unique-users', auth, requireAdmin, async (req, res) => {
  try {
    const { start, end } = parseDateRange(req);

    // Get unique users by IP address
    const uniqueUsers = await TryOnSession.aggregate([
      {
        $match: {
          createdAt: { $gte: start, $lte: end },
          $or: [
            { 'location.ip': { $exists: true, $ne: null } },
            { 'location.userIP': { $exists: true, $ne: null } }
          ]
        }
      },
      {
        $group: {
          _id: {
            $ifNull: ['$location.userIP', '$location.ip']
          },
          firstSession: { $min: '$createdAt' },
          lastSession: { $max: '$createdAt' },
          totalSessions: { $sum: 1 },
          totalInteractions: { $sum: { $size: { $ifNull: ['$interactions', []] } } },
          conversions: { $sum: { $cond: ['$converted', 1, 0] } },
          avgDuration: { $avg: '$duration' },
          devices: { $addToSet: '$device.type' },
          browsers: { $addToSet: '$device.browser' },
          clientIds: { $addToSet: '$clientId' }
        }
      },
      {
        $project: {
          ip: '$_id',
          firstSession: 1,
          lastSession: 1,
          totalSessions: 1,
          totalInteractions: 1,
          conversions: 1,
          conversionRate: {
            $cond: [
              { $gt: ['$totalSessions', 0] },
              { $multiply: [{ $divide: ['$conversions', '$totalSessions'] }, 100] },
              0
            ]
          },
          avgDuration: { $round: ['$avgDuration', 0] },
          devices: 1,
          browsers: 1,
          uniqueClients: { $size: '$clientIds' },
          _id: 0
        }
      },
      { $sort: { totalSessions: -1 } }
    ]);

    // Get summary statistics
    const totalUniqueUsers = uniqueUsers.length;
    const totalSessionsFromUniqueUsers = uniqueUsers.reduce((sum, user) => sum + user.totalSessions, 0);
    const avgSessionsPerUser = totalUniqueUsers > 0 ? totalSessionsFromUniqueUsers / totalUniqueUsers : 0;

    res.json({
      uniqueUsers,
      summary: {
        totalUniqueUsers,
        totalSessionsFromUniqueUsers,
        avgSessionsPerUser: Math.round(avgSessionsPerUser * 100) / 100
      }
    });
  } catch (error) {
    console.error('Unique users analytics error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// GET /api/analytics/admin/clients - Client performance analytics
router.get('/admin/clients', auth, requireAdmin, async (req, res) => {
  try {
    const { start, end } = parseDateRange(req);
    
    const clientAnalytics = await TryOnSession.aggregate([
      {
        $match: {
          createdAt: { $gte: start, $lte: end },
          clientId: { $exists: true, $ne: null }
        }
      },
      {
        $group: {
          _id: '$clientId',
          sessions: { $sum: 1 },
          conversions: { $sum: { $cond: ['$converted', 1, 0] } },
          avgDuration: { $avg: '$duration' },
          totalInteractions: { $sum: { $size: { $ifNull: ['$interactions', []] } } },
          uniqueUsers: { $addToSet: { $ifNull: ['$location.userIP', '$location.ip'] } },
          firstSession: { $min: '$createdAt' },
          lastSession: { $max: '$createdAt' }
        }
      },
      {
        $addFields: {
          clientIdObj: {
            $cond: [
              { $eq: [{ $type: '$_id' }, 'objectId'] },
              '$_id',
              {
                $cond: [
                  { $eq: [{ $strLenCP: { $toString: '$_id' } }, 24] },
                  { $toObjectId: { $toString: '$_id' } },
                  null
                ]
              }
            ]
          }
        }
      },
      {
        $lookup: {
          from: 'users',
          localField: 'clientIdObj',
          foreignField: '_id',
          as: 'client'
        }
      },
      {
        $project: {
          clientId: '$_id',
          companyName: { $ifNull: [{ $arrayElemAt: ['$client.companyName', 0] }, 'Unknown Client'] },
          email: { $ifNull: [{ $arrayElemAt: ['$client.email', 0] }, 'No email'] },
          sessions: 1,
          conversions: 1,
          conversionRate: {
            $cond: [
              { $gt: ['$sessions', 0] },
              { $multiply: [{ $divide: ['$conversions', '$sessions'] }, 100] },
              0
            ]
          },
          avgDuration: { $round: ['$avgDuration', 0] },
          totalInteractions: 1,
          uniqueUsers: { $size: { $ifNull: ['$uniqueUsers', []] } },
          firstSession: 1,
          lastSession: 1,
          daysSinceFirstSession: {
            $divide: [
              { $subtract: [new Date(), '$firstSession'] },
              86400000 // milliseconds in a day
            ]
          }
        }
      },
      { $sort: { sessions: -1 } }
    ]);
    
    res.json(clientAnalytics);
  } catch (error) {
    console.error('Client analytics error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// GET /api/analytics/admin/products - Product performance across all clients
router.get('/admin/products', auth, requireAdmin, async (req, res) => {
  try {
    const { start, end } = parseDateRange(req);

    const productAnalytics = await TryOnSession.aggregate([
      {
        $match: {
          createdAt: { $gte: start, $lte: end },
          productId: { $exists: true, $ne: null }
        }
      },
      {
        $group: {
          _id: '$productId',
          productName: { $first: '$productName' },
          category: { $first: '$productCategory' },
          sessions: { $sum: 1 },
          conversions: { $sum: { $cond: ['$converted', 1, 0] } },
          avgDuration: { $avg: '$duration' },
          totalInteractions: { $sum: { $size: { $ifNull: ['$interactions', []] } } },
          uniqueClients: { $addToSet: '$clientId' },
          firstSeen: { $min: '$createdAt' },
          lastSeen: { $max: '$createdAt' }
        }
      },
      {
        $addFields: {
          conversionRate: {
            $cond: [
              { $gt: ['$sessions', 0] },
              { $multiply: [{ $divide: ['$conversions', '$sessions'] }, 100] },
              0
            ]
          },
          uniqueClientCount: { $size: '$uniqueClients' },
          // Extract domain from product URL for grouping
          productDomain: {
            $let: {
              vars: {
                url: '$_id'
              },
              in: {
                $cond: [
                  { $regexMatch: { input: '$$url', regex: '^https?://' } },
                  {
                    $arrayElemAt: [
                      { $split: [
                        { $arrayElemAt: [{ $split: ['$$url', '/'] }, 2] },
                        '.'
                      ] },
                      -2
                    ]
                  },
                  'unknown'
                ]
              }
            }
          }
        }
      },
      {
        $project: {
          productId: '$_id',
          productName: 1,
          category: 1,
          productDomain: 1,
          sessions: 1,
          conversions: 1,
          conversionRate: { $round: ['$conversionRate', 2] },
          avgDuration: { $round: ['$avgDuration', 0] },
          totalInteractions: 1,
          uniqueClientCount: 1,
          firstSeen: 1,
          lastSeen: 1,
          daysSinceFirstSeen: {
            $divide: [
              { $subtract: [new Date(), '$firstSeen'] },
              86400000 // milliseconds in a day
            ]
          }
        }
      },
      { $sort: { sessions: -1 } },
      { $limit: 100 }
    ]);

    // Get summary statistics
    const totalProducts = productAnalytics.length;
    const totalSessions = productAnalytics.reduce((sum, product) => sum + product.sessions, 0);
    const avgSessionsPerProduct = totalProducts > 0 ? totalSessions / totalProducts : 0;

    res.json({
      products: productAnalytics,
      summary: {
        totalProducts,
        totalSessions,
        avgSessionsPerProduct: Math.round(avgSessionsPerProduct * 100) / 100
      }
    });
  } catch (error) {
    console.error('Product analytics error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// GET /api/analytics/admin/devices - Device technical analytics
router.get('/admin/devices', auth, requireAdmin, async (req, res) => {
  try {
    const { start, end } = parseDateRange(req);
    
    // Get device distribution
    const devices = await TryOnSession.aggregate([
      {
        $match: {
          createdAt: { $gte: start, $lte: end }
        }
      },
      {
        $group: {
          _id: '$device.type',
          count: { $sum: 1 },
          conversions: { $sum: { $cond: ['$converted', 1, 0] } }
        }
      },
      {
        $project: {
          device: '$_id',
          count: 1,
          conversions: 1,
          _id: 0
        }
      }
    ]);

    // Get browser distribution
    const browsers = await TryOnSession.aggregate([
      {
        $match: {
          createdAt: { $gte: start, $lte: end }
        }
      },
      {
        $group: {
          _id: '$device.browser',
          sessions: { $sum: 1 }
        }
      },
      {
        $project: {
          name: '$_id',
          sessions: 1,
          color: {
            $switch: {
              branches: [
                { case: { $eq: ['$_id', 'Chrome'] }, then: '#4285F4' },
                { case: { $eq: ['$_id', 'Firefox'] }, then: '#FF9500' },
                { case: { $eq: ['$_id', 'Safari'] }, then: '#34C759' },
                { case: { $eq: ['$_id', 'Edge'] }, then: '#0078D7' }
              ],
              default: '#6B7280'
            }
          },
          _id: 0
        }
      }
    ]);

    // Get operating system distribution
    const operatingSystems = await TryOnSession.aggregate([
      {
        $match: {
          createdAt: { $gte: start, $lte: end }
        }
      },
      {
        $group: {
          _id: '$device.os',
          sessions: { $sum: 1 }
        }
      },
      {
        $project: {
          name: '$_id',
          sessions: 1,
          color: {
            $switch: {
              branches: [
                { case: { $eq: ['$_id', 'Windows'] }, then: '#00A4EF' },
                { case: { $eq: ['$_id', 'macOS'] }, then: '#000000' },
                { case: { $eq: ['$_id', 'iOS'] }, then: '#000000' },
                { case: { $eq: ['$_id', 'Android'] }, then: '#3DDC84' }
              ],
              default: '#6B7280'
            }
          },
          _id: 0
        }
      }
    ]);

    // Get performance metrics
    const performanceMetrics = await TryOnSession.aggregate([
      {
        $match: {
          createdAt: { $gte: start, $lte: end }
        }
      },
      {
        $group: {
          _id: {
            $dateToString: { format: "%Y-%m-%d", date: "$createdAt" }
          },
          loadTime: { $avg: '$performance.loadTime' },
          errorRate: {
            $avg: {
              $cond: [{ $gt: ['$performance.errors', 0] }, 100, 0]
            }
          },
          uptime: {
            $avg: {
              $cond: [{ $eq: ['$performance.status', 'success'] }, 100, 0]
            }
          }
        }
      },
      {
        $project: {
          date: '$_id',
          loadTime: { $round: ['$loadTime', 2] },
          errorRate: { $round: ['$errorRate', 2] },
          uptime: { $round: ['$uptime', 2] },
          _id: 0
        }
      },
      { $sort: { date: 1 } }
    ]);

    // Calculate metrics
    const totalSessions = devices.reduce((sum, device) => sum + device.count, 0);
    const totalConversions = devices.reduce((sum, device) => sum + device.conversions, 0);
    const avgLoadTime = performanceMetrics.reduce((sum, metric) => sum + metric.loadTime, 0) / performanceMetrics.length;
    const avgErrorRate = performanceMetrics.reduce((sum, metric) => sum + metric.errorRate, 0) / performanceMetrics.length;
    const avgUptime = performanceMetrics.reduce((sum, metric) => sum + metric.uptime, 0) / performanceMetrics.length;

    // Calculate changes from previous period
    const previousStart = new Date(start.getTime() - (end - start));
    const previousEnd = new Date(start);

    const previousMetrics = await TryOnSession.aggregate([
      {
        $match: {
          createdAt: { $gte: previousStart, $lte: previousEnd }
        }
      },
      {
        $group: {
          _id: null,
          avgLoadTime: { $avg: '$performance.loadTime' },
          avgErrorRate: {
            $avg: {
              $cond: [{ $gt: ['$performance.errors', 0] }, 100, 0]
            }
          },
          avgUptime: {
            $avg: {
              $cond: [{ $eq: ['$performance.status', 'success'] }, 100, 0]
            }
          }
        }
      }
    ]);

    const previousAvgLoadTime = previousMetrics[0]?.avgLoadTime || 0;
    const previousAvgErrorRate = previousMetrics[0]?.avgErrorRate || 0;
    const previousAvgUptime = previousMetrics[0]?.avgUptime || 0;

    const loadTimeChange = previousAvgLoadTime ? ((avgLoadTime - previousAvgLoadTime) / previousAvgLoadTime) * 100 : 0;
    const errorRateChange = previousAvgErrorRate ? ((avgErrorRate - previousAvgErrorRate) / previousAvgErrorRate) * 100 : 0;
    const uptimeChange = previousAvgUptime ? ((avgUptime - previousAvgUptime) / previousAvgUptime) * 100 : 0;

    res.json({
      devices,
      browsers,
      operatingSystems,
      performanceMetrics,
      metrics: {
        totalSessions,
        sessionChange: ((totalSessions - (await TryOnSession.countDocuments({ createdAt: { $gte: previousStart, $lte: previousEnd } }))) / totalSessions) * 100,
        avgLoadTime,
        loadTimeChange,
        errorRate: avgErrorRate,
        errorRateChange,
        uptime: avgUptime,
        uptimeChange
      }
    });
  } catch (error) {
    console.error('Error fetching device analytics:', error);
    res.status(500).json({ message: 'Error fetching device analytics' });
  }
});

// GET /api/analytics/admin/geographic - Geographic analytics
router.get('/admin/geographic', auth, requireAdmin, async (req, res) => {
  try {
    const { start, end } = parseDateRange(req);

    const geoStats = await TryOnSession.aggregate([
      {
        $match: {
          createdAt: { $gte: start, $lte: end },
          'location.country': { $exists: true }
        }
      },
      {
        $group: {
          _id: {
            country: '$location.country',
            region: '$location.region'
          },
          sessions: { $sum: 1 },
          conversions: { $sum: { $cond: ['$converted', 1, 0] } },
          revenue: { $sum: '$conversionValue' }
        }
      },
      {
        $project: {
          country: '$_id.country',
          region: '$_id.region',
          sessions: 1,
          conversions: 1,
          conversionRate: {
            $cond: [
              { $gt: ['$sessions', 0] },
              { $multiply: [{ $divide: ['$conversions', '$sessions'] }, 100] },
              0
            ]
          },
          revenue: { $ifNull: ['$revenue', 0] }
        }
      },
      { $sort: { sessions: -1 } }
    ]);

    res.json(geoStats);
  } catch (error) {
    console.error('Geographic analytics error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// GET /api/analytics/admin/user-engagement - User engagement analytics
router.get('/admin/user-engagement', auth, requireAdmin, async (req, res) => {
  try {
    const { start, end } = parseDateRange(req);

    // Get engagement metrics
    const engagementData = await TryOnSession.aggregate([
      {
        $match: {
          createdAt: { $gte: start, $lte: end }
        }
      },
      {
        $group: {
          _id: null,
          avgDuration: { $avg: '$duration' },
          totalInteractions: { $sum: { $size: '$interactions' } },
          avgInteractionsPerSession: { $avg: { $size: '$interactions' } },
          totalSessions: { $sum: 1 },
          completedSessions: { $sum: { $cond: [{ $eq: ['$outcome', 'completed'] }, 1, 0] } },
          sharedSessions: { $sum: { $cond: [{ $eq: ['$outcome', 'shared'] }, 1, 0] } },
          avgTimeToFirstInteraction: { $avg: '$behaviorMetrics.timeToFirstInteraction' },
          cameraInitSuccessRate: {
            $avg: { $cond: ['$behaviorMetrics.cameraInitSuccess', 1, 0] }
          },
          handDetectionSuccessRate: {
            $avg: { $cond: ['$behaviorMetrics.handDetectionSuccess', 1, 0] }
          },
          backgroundRemovalSuccessRate: {
            $avg: { $cond: ['$behaviorMetrics.backgroundRemovalSuccess', 1, 0] }
          },
          avgProductSwitches: { $avg: '$behaviorMetrics.productSwitches' },
          avgPageLoadTime: { $avg: '$performanceMetrics.pageLoadTime' },
          errorRate: {
            $avg: {
              $cond: [
                { $gt: [{ $size: '$performanceMetrics.errors' }, 0] },
                1,
                0
              ]
            }
          }
        }
      }
    ]);

    // Get hourly distribution
    const hourlyData = await TryOnSession.aggregate([
      {
        $match: {
          createdAt: { $gte: start, $lte: end }
        }
      },
      {
        $group: {
          _id: { $hour: '$createdAt' },
          sessions: { $sum: 1 }
        }
      },
      { $sort: { _id: 1 } }
    ]);

    // Get session duration distribution
    const durationData = await TryOnSession.aggregate([
      {
        $match: {
          createdAt: { $gte: start, $lte: end },
          duration: { $exists: true }
        }
      },
      {
        $bucket: {
          groupBy: '$duration',
          boundaries: [0, 30, 60, 120, 300, 600, Infinity],
          default: 'Other',
          output: {
            count: { $sum: 1 }
          }
        }
      }
    ]);

    // Get interaction types distribution
    const interactionTypes = await TryOnSession.aggregate([
      {
        $match: {
          createdAt: { $gte: start, $lte: end }
        }
      },
      { $unwind: '$interactions' },
      {
        $group: {
          _id: '$interactions.type',
          count: { $sum: 1 }
        }
      },
      { $sort: { count: -1 } }
    ]);

    res.json({
      engagement: engagementData[0] || {},
      hourlyDistribution: hourlyData,
      sessionDuration: durationData,
      interactionTypes
    });
  } catch (error) {
    console.error('User engagement analytics error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// GET /api/analytics/admin/business-metrics - Business metrics analytics
router.get('/admin/business-metrics', auth, requireAdmin, async (req, res) => {
  try {
    const { start, end } = parseDateRange(req);

    // Get revenue metrics
    const revenueMetrics = await TryOnSession.aggregate([
      {
        $match: {
          converted: true,
          conversionValue: { $exists: true, $gt: 0 },
          createdAt: { $gte: start, $lte: end }
        }
      },
      {
        $group: {
          _id: null,
          totalRevenue: { $sum: '$conversionValue' },
          avgOrderValue: { $avg: '$conversionValue' },
          totalOrders: { $sum: 1 },
          maxOrderValue: { $max: '$conversionValue' },
          minOrderValue: { $min: '$conversionValue' }
        }
      }
    ]);

    // Get revenue by client
    const revenueByClient = await TryOnSession.aggregate([
      {
        $match: {
          converted: true,
          conversionValue: { $exists: true, $gt: 0 },
          createdAt: { $gte: start, $lte: end }
        }
      },
      {
        $group: {
          _id: '$clientId',
          revenue: { $sum: '$conversionValue' },
          orders: { $sum: 1 }
        }
      },
      {
        $lookup: {
          from: 'users',
          localField: '_id',
          foreignField: '_id',
          as: 'client'
        }
      },
      {
        $unwind: '$client'
      },
      {
        $project: {
          clientName: '$client.companyName',
          revenue: 1,
          orders: 1,
          avgOrderValue: { $divide: ['$revenue', '$orders'] }
        }
      },
      { $sort: { revenue: -1 } },
      { $limit: 10 }
    ]);

    // Get monthly revenue trends
    const monthlyRevenue = await TryOnSession.aggregate([
      {
        $match: {
          converted: true,
          conversionValue: { $exists: true, $gt: 0 },
          createdAt: { $gte: start, $lte: end }
        }
      },
      {
        $group: {
          _id: {
            year: { $year: '$createdAt' },
            month: { $month: '$createdAt' }
          },
          revenue: { $sum: '$conversionValue' },
          orders: { $sum: 1 }
        }
      },
      { $sort: { '_id.year': 1, '_id.month': 1 } }
    ]);

    // Get conversion funnel
    const funnelData = await TryOnSession.aggregate([
      {
        $match: {
          createdAt: { $gte: start, $lte: end }
        }
      },
      {
        $group: {
          _id: null,
          totalSessions: { $sum: 1 },
          completedSessions: { $sum: { $cond: [{ $eq: ['$outcome', 'completed'] }, 1, 0] } },
          sharedSessions: { $sum: { $cond: [{ $eq: ['$outcome', 'shared'] }, 1, 0] } },
          convertedSessions: { $sum: { $cond: ['$converted', 1, 0] } }
        }
      }
    ]);

    res.json({
      revenue: revenueMetrics[0] || {},
      revenueByClient,
      monthlyRevenue,
      funnel: funnelData[0] || {}
    });
  } catch (error) {
    console.error('Business metrics analytics error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// GET /api/analytics/admin/tryon - Try-On Analytics
router.get('/admin/tryon', auth, requireAdmin, async (req, res) => {
  try {
    const { start, end } = parseDateRange(req);
    
    // Get try-on session statistics - focus on actual data
    const tryOnStats = await TryOnSession.aggregate([
      {
        $match: {
          createdAt: { $gte: start, $lte: end }
        }
      },
      {
        $addFields: {
          calculatedDuration: {
            $cond: [
              { $and: [{ $ne: ['$startTime', null] }, { $ne: ['$updatedAt', null] }] },
              { $divide: [{ $subtract: ['$updatedAt', '$startTime'] }, 1000] },
              0
            ]
          }
        }
      },
      {
        $group: {
          _id: {
            $dateToString: { format: "%Y-%m-%d", date: "$createdAt" }
          },
          sessions: { $sum: 1 },
          avgDuration: { $avg: '$calculatedDuration' }
        }
      },
      { $sort: { _id: 1 } }
    ]);

    // Get product category distribution based on actual data
    const categoryStats = await TryOnSession.aggregate([
      {
        $match: {
          createdAt: { $gte: start, $lte: end },
          productCategory: { $exists: true, $ne: null }
        }
      },
      {
        $group: {
          _id: '$productCategory',
          sessions: { $sum: 1 }
        }
      },
      {
        $project: {
          category: '$_id',
          sessions: 1,
          _id: 0
        }
      },
      { $sort: { sessions: -1 } }
    ]);

    res.json({
      dailyStats: tryOnStats.map(stat => ({
        date: stat._id,
        sessions: stat.sessions,
        avgDuration: Math.round(stat.avgDuration || 0)
      })),
      categoryStats
    });
  } catch (error) {
    console.error('Error fetching try-on analytics:', error);
    res.status(500).json({ message: 'Error fetching try-on analytics' });
  }
});

// GET /api/analytics/admin/client-performance - Client Performance Analytics
router.get('/admin/client-performance', auth, requireAdmin, async (req, res) => {
  try {
    const { start, end } = parseDateRange(req);
    
    // Get client performance metrics - focus on actual session data
    const clientStats = await TryOnSession.aggregate([
      {
        $match: {
          createdAt: { $gte: start, $lte: end },
          clientId: { $exists: true, $ne: null }
        }
      },
      {
        $addFields: {
          calculatedDuration: {
            $cond: [
              { $and: [{ $ne: ['$startTime', null] }, { $ne: ['$updatedAt', null] }] },
              { $divide: [{ $subtract: ['$updatedAt', '$startTime'] }, 1000] },
              0
            ]
          },
          clientIdObj: {
            $cond: [
              { $eq: [{ $type: '$clientId' }, 'objectId'] },
              '$clientId',
              {
                $cond: [
                  { $eq: [{ $strLenCP: { $toString: '$clientId' } }, 24] },
                  { $toObjectId: { $toString: '$clientId' } },
                  null
                ]
              }
            ]
          }
        }
      },
      {
        $group: {
          _id: '$clientIdObj',
          sessions: { $sum: 1 },
          avgDuration: { $avg: '$calculatedDuration' }
        }
      },
      {
        $lookup: {
          from: 'users',
          localField: '_id',
          foreignField: '_id',
          as: 'client'
        }
      },
      {
        $unwind: {
          path: '$client',
          preserveNullAndEmptyArrays: true
        }
      },
      {
        $project: {
          clientName: { $ifNull: ['$client.companyName', 'Unknown Client'] },
          email: { $ifNull: ['$client.email', 'No email'] },
          sessions: 1,
          avgDuration: { $round: [{ $ifNull: ['$avgDuration', 0] }, 1] }
        }
      },
      { $sort: { sessions: -1 } }
    ]);

    res.json({
      clients: clientStats
    });
  } catch (error) {
    console.error('Error fetching client performance analytics:', error);
    res.status(500).json({ message: 'Error fetching client performance analytics' });
  }
});

// GET /api/analytics/admin/product - Product Analytics
router.get('/admin/product', auth, requireAdmin, async (req, res) => {
  try {
    const { start, end } = parseDateRange(req);
    
    // Get product performance metrics based on actual session structure
    const productStats = await TryOnSession.aggregate([
      {
        $match: {
          createdAt: { $gte: start, $lte: end },
          productId: { $exists: true, $ne: null }
        }
      },
      {
        $addFields: {
          calculatedDuration: {
            $cond: [
              { $and: [{ $ne: ['$startTime', null] }, { $ne: ['$updatedAt', null] }] },
              { $divide: [{ $subtract: ['$updatedAt', '$startTime'] }, 1000] },
              0
            ]
          }
        }
      },
      {
        $group: {
          _id: '$productId',
          name: { $first: '$productName' },
          category: { $first: '$productCategory' },
          views: { $sum: 1 },
          avgViewDuration: { $avg: '$calculatedDuration' }
        }
      },
      {
        $project: {
          name: { $ifNull: ['$name', 'Unknown Product'] },
          category: { $ifNull: ['$category', 'Unknown'] },
          views: 1,
          avgViewDuration: { $ifNull: ['$avgViewDuration', 0] }
        }
      },
      { $sort: { views: -1 } }
    ]);

    res.json({
      products: productStats.map(product => ({
        ...product,
        avgViewDuration: Math.round(product.avgViewDuration)
      }))
    });
  } catch (error) {
    console.error('Error fetching product analytics:', error);
    res.status(500).json({ message: 'Error fetching product analytics' });
  }
});

// GET /api/analytics/admin/user-engagement - User Engagement Analytics
router.get('/admin/user-engagement', auth, requireAdmin, async (req, res) => {
  try {
    const { start, end } = parseDateRange(req);
    
    // Get engagement metrics
    const engagementData = await TryOnSession.aggregate([
      {
        $match: {
          createdAt: { $gte: start, $lte: end }
        }
      },
      {
        $group: {
          _id: null,
          avgDuration: { $avg: '$duration' },
          totalInteractions: {
            $sum: {
              $cond: [
                { $isArray: '$interactions' },
                { $size: '$interactions' },
                0
              ]
            }
          },
          avgInteractionsPerSession: {
            $avg: {
              $cond: [
                { $isArray: '$interactions' },
                { $size: '$interactions' },
                0
              ]
            }
          },
          totalSessions: { $sum: 1 },
          completedSessions: { $sum: { $cond: [{ $eq: ['$outcome', 'completed'] }, 1, 0] } },
          sharedSessions: { $sum: { $cond: [{ $eq: ['$outcome', 'shared'] }, 1, 0] } },
          avgTimeToFirstInteraction: { $avg: '$behaviorMetrics.timeToFirstInteraction' },
          cameraInitSuccessRate: {
            $avg: { $cond: ['$behaviorMetrics.cameraInitSuccess', 1, 0] }
          },
          handDetectionSuccessRate: {
            $avg: { $cond: ['$behaviorMetrics.handDetectionSuccess', 1, 0] }
          },
          backgroundRemovalSuccessRate: {
            $avg: { $cond: ['$behaviorMetrics.backgroundRemovalSuccess', 1, 0] }
          },
          avgProductSwitches: { $avg: '$behaviorMetrics.productSwitches' },
          avgPageLoadTime: { $avg: '$performanceMetrics.pageLoadTime' },
          errorRate: {
            $avg: {
              $cond: [
                {
                  $and: [
                    { $isArray: '$performanceMetrics.errors' },
                    { $gt: [{ $size: '$performanceMetrics.errors' }, 0] }
                  ]
                },
                1,
                0
              ]
            }
          }
        }
      }
    ]);

    // Get hourly distribution
    const hourlyData = await TryOnSession.aggregate([
      {
        $match: {
          createdAt: { $gte: start, $lte: end }
        }
      },
      {
        $group: {
          _id: { $hour: '$createdAt' },
          sessions: { $sum: 1 }
        }
      },
      { $sort: { _id: 1 } }
    ]);

    // Get session duration distribution
    const durationData = await TryOnSession.aggregate([
      {
        $match: {
          createdAt: { $gte: start, $lte: end },
          duration: { $exists: true }
        }
      },
      {
        $bucket: {
          groupBy: '$duration',
          boundaries: [0, 30, 60, 120, 300, 600, Infinity],
          default: 'Other',
          output: {
            count: { $sum: 1 }
          }
        }
      }
    ]);

    // Get interaction types distribution
    const interactionTypes = await TryOnSession.aggregate([
      {
        $match: {
          createdAt: { $gte: start, $lte: end },
          interactions: { $exists: true, $ne: [] }
        }
      },
      { $unwind: '$interactions' },
      {
        $group: {
          _id: '$interactions.type',
          count: { $sum: 1 }
        }
      },
      { $sort: { count: -1 } }
    ]);

    res.json({
      engagement: engagementData[0] || {},
      hourlyDistribution: hourlyData,
      sessionDuration: durationData,
      interactionTypes
    });
  } catch (error) {
    console.error('User engagement analytics error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// GET /api/analytics/admin/business - Business Metrics
router.get('/admin/business', auth, requireAdmin, async (req, res) => {
  try {
    const { start, end } = parseDateRange(req);
    
    // Get business metrics
    const businessStats = await TryOnSession.aggregate([
      {
        $match: {
          createdAt: { $gte: start, $lte: end },
          converted: true
        }
      },
      {
        $group: {
          _id: {
            $dateToString: { format: "%Y-%m-%d", date: "$createdAt" }
          },
          revenue: { $sum: '$conversionValue' },
          orders: { $sum: 1 },
          avgOrderValue: { $avg: '$conversionValue' }
        }
      },
      { $sort: { _id: 1 } }
    ]);

    // Calculate total metrics
    const totalRevenue = businessStats.reduce((sum, day) => sum + day.revenue, 0);
    const totalOrders = businessStats.reduce((sum, day) => sum + day.orders, 0);
    const avgOrderValue = totalOrders > 0 ? totalRevenue / totalOrders : 0;

    res.json({
      dailyStats: businessStats.map(stat => ({
        date: stat._id,
        revenue: stat.revenue,
        orders: stat.orders,
        avgOrderValue: Math.round(stat.avgOrderValue * 100) / 100
      })),
      totals: {
        revenue: totalRevenue,
        orders: totalOrders,
        avgOrderValue: Math.round(avgOrderValue * 100) / 100
      }
    });
  } catch (error) {
    console.error('Error fetching business metrics:', error);
    res.status(500).json({ message: 'Error fetching business metrics' });
  }
});

// GET /api/analytics/admin/geographic - Geographic Data
router.get('/admin/geographic', auth, requireAdmin, async (req, res) => {
  try {
    const { start, end } = parseDateRange(req);



    console.log('Geographic analytics request:', { start, end });

    // First, let's check what location data we have
    const locationDataCheck = await TryOnSession.aggregate([
      {
        $match: {
          createdAt: { $gte: start, $lte: end }
        }
      },
      {
        $group: {
          _id: null,
          totalSessions: { $sum: 1 },
          hasTimezone: { $sum: { $cond: [{ $ne: ['$location.timezone', null] }, 1, 0] } },
          hasIP: { $sum: { $cond: [{ $ne: ['$location.ip', null] }, 1, 0] } },
          hasUserIP: { $sum: { $cond: [{ $ne: ['$location.userIP', null] }, 1, 0] } },
          sampleTimezones: { $addToSet: '$location.timezone' },
          sampleIPs: { $addToSet: '$location.userIP' }
        }
      }
    ]);

    console.log('Location data check:', locationDataCheck[0]);

    // If no sessions found, return empty data
    if (!locationDataCheck[0] || locationDataCheck[0].totalSessions === 0) {
      console.log('No sessions found in date range');
      return res.json({
        countries: [],
        continents: [],
        cities: []
      });
    }

    // Get geographic distribution based on timezone and unique IPs from actual data structure
    const geoStats = await TryOnSession.aggregate([
      {
        $match: {
          createdAt: { $gte: start, $lte: end }
        }
      },
      {
        $addFields: {
          extractedCountry: {
            $cond: [
              { $and: [{ $ne: ['$location.country', null] }, { $ne: ['$location.country', ''] }] },
              '$location.country',
              {
                $cond: [
                  { $ne: ['$location.timezone', null] },
                  {
                    $switch: {
                      branches: [
                        { case: { $eq: ['$location.timezone', 'Africa/Cairo'] }, then: 'Egypt' },
                        { case: { $eq: ['$location.timezone', 'Africa/Lagos'] }, then: 'Nigeria' },
                        { case: { $eq: ['$location.timezone', 'Africa/Johannesburg'] }, then: 'South Africa' },
                        { case: { $eq: ['$location.timezone', 'Europe/London'] }, then: 'United Kingdom' },
                        { case: { $eq: ['$location.timezone', 'Europe/Paris'] }, then: 'France' },
                        { case: { $eq: ['$location.timezone', 'America/New_York'] }, then: 'United States' },
                        { case: { $eq: ['$location.timezone', 'America/Los_Angeles'] }, then: 'United States' },
                        { case: { $eq: ['$location.timezone', 'Asia/Tokyo'] }, then: 'Japan' },
                        { case: { $eq: ['$location.timezone', 'Asia/Shanghai'] }, then: 'China' },
                        { case: { $eq: ['$location.timezone', 'Asia/Dubai'] }, then: 'UAE' },
                        { case: { $eq: ['$location.timezone', 'Asia/Kolkata'] }, then: 'India' },
                        { case: { $eq: ['$location.timezone', 'Australia/Sydney'] }, then: 'Australia' }
                      ],
                      default: { $arrayElemAt: [{ $split: ['$location.timezone', '/'] }, 0] }
                    }
                  },
                  'Unknown'
                ]
              }
            ]
          },
          userIP: {
            $cond: [
              { $ne: ['$location.userIP', null] },
              '$location.userIP',
              {
                $cond: [
                  { $ne: ['$location.ip', null] },
                  '$location.ip',
                  'Unknown'
                ]
              }
            ]
          }
        }
      },
      {
        $group: {
          _id: '$extractedCountry',
          sessions: { $sum: 1 },
          uniqueIPs: { $addToSet: '$userIP' }
        }
      },
      {
        $project: {
          country: '$_id',
          sessions: 1,
          uniqueUsers: { $size: '$uniqueIPs' },
          _id: 0
        }
      },
      { $sort: { sessions: -1 } }
    ]);

    // Get continent-level data
    const continentStats = await TryOnSession.aggregate([
      {
        $match: {
          createdAt: { $gte: start, $lte: end }
        }
      },
      {
        $addFields: {
          continent: {
            $cond: [
              { $ne: ['$location.timezone', null] },
              {
                $switch: {
                  branches: [
                    { case: { $regexMatch: { input: '$location.timezone', regex: '^Africa/' } }, then: 'Africa' },
                    { case: { $regexMatch: { input: '$location.timezone', regex: '^Europe/' } }, then: 'Europe' },
                    { case: { $regexMatch: { input: '$location.timezone', regex: '^America/' } }, then: 'Americas' },
                    { case: { $regexMatch: { input: '$location.timezone', regex: '^Asia/' } }, then: 'Asia' },
                    { case: { $regexMatch: { input: '$location.timezone', regex: '^Australia/' } }, then: 'Oceania' }
                  ],
                  default: 'Unknown'
                }
              },
              'Unknown'
            ]
          },
          userIP: {
            $cond: [
              { $ne: ['$location.userIP', null] },
              '$location.userIP',
              {
                $cond: [
                  { $ne: ['$location.ip', null] },
                  '$location.ip',
                  'Unknown'
                ]
              }
            ]
          }
        }
      },
      {
        $group: {
          _id: '$continent',
          sessions: { $sum: 1 },
          uniqueIPs: { $addToSet: '$userIP' }
        }
      },
      {
        $project: {
          continent: '$_id',
          sessions: 1,
          uniqueUsers: { $size: '$uniqueIPs' },
          _id: 0
        }
      },
      { $sort: { sessions: -1 } }
    ]);

    // Filter out 'Unknown' entries and log results
    const filteredCountries = geoStats.filter(country => country.country !== 'Unknown' && country.country !== null);
    const filteredContinents = continentStats.filter(continent => continent.continent !== 'Unknown' && continent.continent !== null);

    console.log('Geographic analytics results:', {
      totalCountries: filteredCountries.length,
      totalContinents: filteredContinents.length,
      countries: filteredCountries,
      continents: filteredContinents
    });

    // If no geographic data found, create fallback data based on IP addresses
    let finalCountries = filteredCountries;
    let finalContinents = filteredContinents;

    if (filteredCountries.length === 0 && locationDataCheck[0].hasUserIP > 0) {
      console.log('No timezone data found, creating fallback geographic data based on IPs');

      // Create fallback data grouped by IP ranges or just show "Unknown Location"
      const ipBasedStats = await TryOnSession.aggregate([
        {
          $match: {
            createdAt: { $gte: start, $lte: end },
            $or: [
              { 'location.userIP': { $exists: true, $ne: null } },
              { 'location.ip': { $exists: true, $ne: null } }
            ]
          }
        },
        {
          $group: {
            _id: 'Unknown Location',
            sessions: { $sum: 1 },
            uniqueIPs: {
              $addToSet: {
                $cond: [
                  { $ne: ['$location.userIP', null] },
                  '$location.userIP',
                  '$location.ip'
                ]
              }
            }
          }
        },
        {
          $project: {
            country: '$_id',
            sessions: 1,
            uniqueUsers: { $size: '$uniqueIPs' },
            _id: 0
          }
        }
      ]);

      if (ipBasedStats.length > 0) {
        finalCountries = ipBasedStats;
        finalContinents = [{
          continent: 'Unknown',
          sessions: ipBasedStats[0].sessions,
          uniqueUsers: ipBasedStats[0].uniqueUsers
        }];
      }
    }

    res.json({
      countries: finalCountries,
      continents: finalContinents,
      cities: [] // Not available since city data is null
    });
  } catch (error) {
    console.error('Error fetching geographic data:', error);
    res.status(500).json({ message: 'Error fetching geographic data' });
  }
});

// GET /api/analytics/admin/geographic-test - Test endpoint for geographic analytics
router.get('/admin/geographic-test', auth, requireAdmin, async (req, res) => {
  try {
    console.log('Geographic test endpoint called');

    // Return sample data to test frontend
    const testData = {
      countries: [
        { country: 'Egypt', sessions: 15, uniqueUsers: 8 },
        { country: 'United States', sessions: 12, uniqueUsers: 6 },
        { country: 'United Kingdom', sessions: 8, uniqueUsers: 4 }
      ],
      continents: [
        { continent: 'Africa', sessions: 15, uniqueUsers: 8 },
        { continent: 'Americas', sessions: 12, uniqueUsers: 6 },
        { continent: 'Europe', sessions: 8, uniqueUsers: 4 }
      ],
      cities: []
    };

    console.log('Returning test data:', testData);
    res.json(testData);
  } catch (error) {
    console.error('Error in geographic test endpoint:', error);
    res.status(500).json({ message: 'Error in test endpoint' });
  }
});

// Client Analytics Routes (for individual clients)

// GET /api/analytics/client/test - Simple test endpoint
router.get('/client/test', (req, res) => {
  console.log('Client test endpoint called');
  res.json({ message: 'Client analytics routes are working', timestamp: new Date().toISOString() });
});

// GET /api/analytics/client/overview - Client overview analytics
router.get('/client/overview', auth, requireClientAccess, async (req, res) => {
  try {
    const { start, end } = parseDateRange(req);
    
    // Calculate previous period dates
    const periodDuration = end - start;
    const previousStart = new Date(start.getTime() - periodDuration);
    const previousEnd = new Date(start);

    // Get client ID from the authenticated user
    const clientId = req.user.role === 'admin' ? req.query.clientId : req.user._id;
    if (!clientId) {
      return res.status(400).json({ message: 'Client ID is required' });
    }

    // Convert clientId to ObjectId if it's a string
    const clientObjectId = toObjectId(clientId);
    if (!clientObjectId) {
      console.error('Invalid clientId format:', clientId);
      return res.status(400).json({ message: 'Invalid client ID format' });
    }
    
    // Get current period statistics
    const totalSessions = await TryOnSession.countDocuments({
      clientId: clientObjectId,
      createdAt: { $gte: start, $lte: end }
    });

    const uniqueUsers = await TryOnSession.distinct('userId', {
      clientId: clientObjectId,
      createdAt: { $gte: start, $lte: end }
    }).length;

    // Calculate average duration and total interactions
    const sessionStats = await TryOnSession.aggregate([
      {
        $match: {
          clientId: clientObjectId,
          createdAt: { $gte: start, $lte: end }
        }
      },
      {
        $group: {
          _id: null,
          avgDuration: { $avg: { $subtract: ['$updatedAt', '$startTime'] } },
          totalInteractions: { $sum: '$interactions' }
        }
      }
    ]);

    // Get previous period statistics
    const previousStats = await TryOnSession.aggregate([
      {
        $match: {
          clientId: clientObjectId,
          createdAt: { $gte: previousStart, $lte: previousEnd }
        }
      },
      {
        $group: {
          _id: null,
          totalSessions: { $sum: 1 },
          avgDuration: { $avg: { $subtract: ['$updatedAt', '$startTime'] } },
          totalInteractions: { $sum: '$interactions' },
          uniqueUsers: { $addToSet: '$userId' }
        }
      }
    ]);

    // Get trends data
    const trends = await TryOnSession.aggregate([
      {
        $match: {
          clientId: clientObjectId,
          createdAt: { $gte: start, $lte: end }
        }
      },
      {
        $group: {
          _id: { $dateToString: { format: '%Y-%m-%d', date: '$createdAt' } },
          sessions: { $sum: 1 }
        }
      },
      {
        $sort: { _id: 1 }
      }
    ]);

    // Get device distribution
    const devices = await TryOnSession.aggregate([
      {
        $match: {
          clientId: clientObjectId,
          createdAt: { $gte: start, $lte: end }
        }
      },
      {
        $group: {
          _id: '$device.type',
          sessions: { $sum: 1 }
        }
      }
    ]);

    // Get recent activity
    const recentActivity = await TryOnSession.find({
      clientId: clientObjectId,
      createdAt: { $gte: start, $lte: end }
    })
    .sort({ createdAt: -1 })
    .limit(5)
    .select('createdAt device.type interactions')
    .lean();

    // Format recent activity
    const formattedActivity = recentActivity.map(session => ({
      type: 'tryon',
      title: 'New Try-On Session',
      description: `${session.device.type} device - ${session.interactions} interactions`,
      timeAgo: getTimeAgo(session.createdAt)
    }));

    // Prepare response data
    const response = {
      totalSessions,
      uniqueUsers,
      avgDuration: sessionStats[0]?.avgDuration ? Math.round(sessionStats[0].avgDuration / 1000) : 0,
      totalInteractions: sessionStats[0]?.totalInteractions || 0,
      previousPeriod: {
        totalSessions: previousStats[0]?.totalSessions || 0,
        avgDuration: previousStats[0]?.avgDuration ? Math.round(previousStats[0].avgDuration / 1000) : 0,
        totalInteractions: previousStats[0]?.totalInteractions || 0,
        uniqueUsers: previousStats[0]?.uniqueUsers?.length || 0
      },
      trends: trends.map(trend => ({
        date: trend._id,
        sessions: trend.sessions
      })),
      devices,
      recentActivity: formattedActivity
    };

    res.json(response);
  } catch (error) {
    console.error('Error fetching client overview:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// GET /api/analytics/client/product-performance - Client's product analytics
router.get('/client/product-performance', auth, requireClientAccess, async (req, res) => {
  try {
    const { start, end } = parseDateRange(req);
    const clientId = req.clientId || req.user.id;

    // Ensure clientId is properly converted to ObjectId
    const clientObjectId = toObjectId(clientId);
    if (!clientObjectId) {
      console.error('Invalid clientId format:', clientId);
      return res.status(400).json({ message: 'Invalid client ID format' });
    }

    const productAnalytics = await TryOnSession.aggregate([
      {
        $match: {
          clientId: clientObjectId,
          createdAt: { $gte: start, $lte: end }
        }
      },
      {
        $group: {
          _id: '$productId',
          productName: { $first: '$productName' },
          category: { $first: '$productCategory' },
          sessions: { $sum: 1 },
          conversions: { $sum: { $cond: ['$converted', 1, 0] } },
          avgDuration: { $avg: '$duration' },
          totalRevenue: { $sum: '$conversionValue' },
          totalInteractions: {
            $sum: {
              $cond: [
                { $isArray: '$interactions' },
                { $size: '$interactions' },
                0
              ]
            }
          }
        }
      },
      {
        $addFields: {
          conversionRate: {
            $cond: [
              { $gt: ['$sessions', 0] },
              { $multiply: [{ $divide: ['$conversions', '$sessions'] }, 100] },
              0
            ]
          }
        }
      },
      { $sort: { sessions: -1 } }
    ]);

    res.json(productAnalytics);
  } catch (error) {
    console.error('Product performance analytics error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// GET /api/analytics/client/user-engagement - Client's user engagement analytics
router.get('/client/user-engagement', auth, requireClientAccess, async (req, res) => {
  try {
    const { start, end } = parseDateRange(req);
    const clientId = req.clientId || req.user.id;

    // Ensure clientId is properly converted to ObjectId
    const clientObjectId = toObjectId(clientId);
    if (!clientObjectId) {
      console.error('Invalid clientId format:', clientId);
      return res.status(400).json({ message: 'Invalid client ID format' });
    }

    // Get engagement metrics
    const engagementData = await TryOnSession.aggregate([
      {
        $match: {
          clientId: clientObjectId,
          createdAt: { $gte: start, $lte: end }
        }
      },
      {
        $group: {
          _id: null,
          avgDuration: { $avg: '$duration' },
          totalInteractions: { $sum: { $size: '$interactions' } },
          avgInteractionsPerSession: { $avg: { $size: '$interactions' } },
          totalSessions: { $sum: 1 },
          completedSessions: { $sum: { $cond: [{ $eq: ['$outcome', 'completed'] }, 1, 0] } },
          sharedSessions: { $sum: { $cond: [{ $eq: ['$outcome', 'shared'] }, 1, 0] } }
        }
      }
    ]);

    // Get hourly distribution
    const hourlyData = await TryOnSession.aggregate([
      {
        $match: {
          clientId: clientObjectId,
          createdAt: { $gte: start, $lte: end }
        }
      },
      {
        $group: {
          _id: { $hour: '$createdAt' },
          sessions: { $sum: 1 }
        }
      },
      { $sort: { _id: 1 } }
    ]);

    // Get interaction types distribution
    const interactionTypes = await TryOnSession.aggregate([
      {
        $match: {
          clientId: clientObjectId,
          createdAt: { $gte: start, $lte: end }
        }
      },
      { $unwind: '$interactions' },
      {
        $group: {
          _id: '$interactions.type',
          count: { $sum: 1 }
        }
      },
      { $sort: { count: -1 } }
    ]);

    res.json({
      engagement: engagementData[0] || {},
      hourlyDistribution: hourlyData,
      interactionTypes
    });
  } catch (error) {
    console.error('User engagement analytics error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// GET /api/analytics/client/conversion-rates - Client's conversion analytics
router.get('/client/conversion-rates', auth, requireClientAccess, async (req, res) => {
  try {
    const { start, end } = parseDateRange(req);
    const clientId = req.clientId || req.user.id;

    // Ensure clientId is properly converted to ObjectId
    const clientObjectId = toObjectId(clientId);
    if (!clientObjectId) {
      console.error('Invalid clientId format:', clientId);
      return res.status(400).json({ message: 'Invalid client ID format' });
    }

    // Get conversion funnel data
    const funnelData = await TryOnSession.aggregate([
      {
        $match: {
          clientId: clientObjectId,
          createdAt: { $gte: start, $lte: end }
        }
      },
      {
        $group: {
          _id: null,
          totalSessions: { $sum: 1 },
          completedSessions: { $sum: { $cond: [{ $eq: ['$outcome', 'completed'] }, 1, 0] } },
          sharedSessions: { $sum: { $cond: [{ $eq: ['$outcome', 'shared'] }, 1, 0] } },
          convertedSessions: { $sum: { $cond: ['$converted', 1, 0] } },
          totalRevenue: { $sum: '$conversionValue' }
        }
      }
    ]);

    // Get conversion by product category
    const categoryConversions = await TryOnSession.aggregate([
      {
        $match: {
          clientId: clientObjectId,
          createdAt: { $gte: start, $lte: end }
        }
      },
      {
        $group: {
          _id: '$productCategory',
          sessions: { $sum: 1 },
          conversions: { $sum: { $cond: ['$converted', 1, 0] } },
          revenue: { $sum: '$conversionValue' }
        }
      },
      {
        $addFields: {
          conversionRate: {
            $cond: [
              { $gt: ['$sessions', 0] },
              { $multiply: [{ $divide: ['$conversions', '$sessions'] }, 100] },
              0
            ]
          }
        }
      }
    ]);

    res.json({
      funnel: funnelData[0] || {},
      categoryConversions
    });
  } catch (error) {
    console.error('Conversion rates analytics error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// GET /api/analytics/client/time-analysis - Client's time-based analytics
router.get('/client/time-analysis', auth, requireClientAccess, async (req, res) => {
  try {
    const { start, end } = parseDateRange(req);
    const clientId = req.clientId || req.user.id;

    // Ensure clientId is properly converted to ObjectId
    const clientObjectId = toObjectId(clientId);
    if (!clientObjectId) {
      console.error('Invalid clientId format:', clientId);
      return res.status(400).json({ message: 'Invalid client ID format' });
    }

    // Get daily trends
    const dailyTrends = await TryOnSession.aggregate([
      {
        $match: {
          clientId: clientObjectId,
          createdAt: { $gte: start, $lte: end }
        }
      },
      {
        $group: {
          _id: {
            $dateToString: { format: "%Y-%m-%d", date: "$createdAt" }
          },
          sessions: { $sum: 1 },
          conversions: { $sum: { $cond: ['$converted', 1, 0] } },
          avgDuration: { $avg: '$duration' },
          revenue: { $sum: '$conversionValue' }
        }
      },
      { $sort: { _id: 1 } }
    ]);

    // Get hourly patterns
    const hourlyPatterns = await TryOnSession.aggregate([
      {
        $match: {
          clientId: clientObjectId,
          createdAt: { $gte: start, $lte: end }
        }
      },
      {
        $group: {
          _id: { $hour: '$createdAt' },
          sessions: { $sum: 1 },
          avgDuration: { $avg: '$duration' }
        }
      },
      { $sort: { _id: 1 } }
    ]);

    // Get day of week patterns
    const weeklyPatterns = await TryOnSession.aggregate([
      {
        $match: {
          clientId: clientObjectId,
          createdAt: { $gte: start, $lte: end }
        }
      },
      {
        $group: {
          _id: { $dayOfWeek: '$createdAt' },
          sessions: { $sum: 1 },
          conversions: { $sum: { $cond: ['$converted', 1, 0] } }
        }
      },
      { $sort: { _id: 1 } }
    ]);

    res.json({
      dailyTrends,
      hourlyPatterns,
      weeklyPatterns
    });
  } catch (error) {
    console.error('Time analysis analytics error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// GET /api/analytics/client/device-stats - Client's device analytics
router.get('/client/device-stats', auth, requireClientAccess, async (req, res) => {
  try {
    const { start, end } = parseDateRange(req);
    const clientId = req.clientId || req.user.id;

    // Ensure clientId is properly converted to ObjectId
    const clientObjectId = toObjectId(clientId);
    if (!clientObjectId) {
      console.error('Invalid clientId format:', clientId);
      return res.status(400).json({ message: 'Invalid client ID format' });
    }

    const deviceStats = await TryOnSession.aggregate([
      {
        $match: {
          clientId: clientObjectId,
          createdAt: { $gte: start, $lte: end },
          'device.type': { $exists: true }
        }
      },
      {
        $group: {
          _id: {
            type: '$device.type',
            os: '$device.os',
            browser: '$device.browser'
          },
          sessions: { $sum: 1 },
          conversions: { $sum: { $cond: ['$converted', 1, 0] } },
          avgDuration: { $avg: '$duration' }
        }
      },
      {
        $group: {
          _id: '$_id.type',
          sessions: { $sum: '$sessions' },
          conversions: { $sum: '$conversions' },
          avgDuration: { $avg: '$avgDuration' },
          details: {
            $push: {
              os: '$_id.os',
              browser: '$_id.browser',
              sessions: '$sessions',
              conversions: '$conversions'
            }
          }
        }
      },
      {
        $addFields: {
          conversionRate: {
            $cond: [
              { $gt: ['$sessions', 0] },
              { $multiply: [{ $divide: ['$conversions', '$sessions'] }, 100] },
              0
            ]
          }
        }
      },
      { $sort: { sessions: -1 } }
    ]);

    res.json(deviceStats);
  } catch (error) {
    console.error('Device stats analytics error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// GET /api/analytics/client/geographic-data - Client's geographic analytics
router.get('/client/geographic-data', auth, requireClientAccess, async (req, res) => {
  try {
    const { start, end } = parseDateRange(req);
    const clientId = req.clientId || req.user.id;

    // Ensure clientId is properly converted to ObjectId
    const clientObjectId = toObjectId(clientId);
    if (!clientObjectId) {
      console.error('Invalid clientId format:', clientId);
      return res.status(400).json({ message: 'Invalid client ID format' });
    }

    const geoStats = await TryOnSession.aggregate([
      {
        $match: {
          clientId: clientObjectId,
          createdAt: { $gte: start, $lte: end },
          'location.country': { $exists: true }
        }
      },
      {
        $group: {
          _id: {
            country: '$location.country',
            region: '$location.region',
            city: '$location.city'
          },
          sessions: { $sum: 1 },
          conversions: { $sum: { $cond: ['$converted', 1, 0] } },
          revenue: { $sum: '$conversionValue' },
          avgDuration: { $avg: '$duration' }
        }
      },
      {
        $addFields: {
          conversionRate: {
            $cond: [
              { $gt: ['$sessions', 0] },
              { $multiply: [{ $divide: ['$conversions', '$sessions'] }, 100] },
              0
            ]
          }
        }
      },
      { $sort: { sessions: -1 } }
    ]);

    res.json(geoStats);
  } catch (error) {
    console.error('Geographic analytics error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// GET /api/analytics/client/recent-activity - Client's recent activity
router.get('/client/recent-activity', auth, requireClientAccess, async (req, res) => {
  try {
    const { start, end } = parseDateRange(req);
    const clientId = req.clientId || req.user.id;

    // Ensure clientId is properly converted to ObjectId
    const clientObjectId = toObjectId(clientId);
    if (!clientObjectId) {
      console.error('Invalid clientId format:', clientId);
      return res.status(400).json({ message: 'Invalid client ID format' });
    }

    // Get recent try-on sessions for this client
    const recentSessions = await TryOnSession.find({
      clientId: clientObjectId,
      createdAt: { $gte: start, $lte: end }
    })
      .sort({ createdAt: -1 })
      .limit(10)
      .select('createdAt converted outcome productName location.country');

    // Format activities
    const activities = [];

    // Helper function to calculate time ago
    const getTimeAgo = (date) => {
      const now = new Date();
      const diffInMs = now - new Date(date);
      const diffInMinutes = Math.floor(diffInMs / (1000 * 60));
      const diffInHours = Math.floor(diffInMs / (1000 * 60 * 60));
      const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));

      if (diffInMinutes < 60) {
        return `${diffInMinutes} minutes ago`;
      } else if (diffInHours < 24) {
        return `${diffInHours} hours ago`;
      } else {
        return `${diffInDays} days ago`;
      }
    };

    // Add recent sessions
    recentSessions.forEach(session => {
      const timeAgo = getTimeAgo(session.createdAt);

      if (session.converted) {
        activities.push({
          type: 'conversion',
          title: 'Purchase Completed',
          description: `Customer purchased ${session.productName || 'product'} after try-on`,
          timeAgo
        });
      } else if (session.outcome === 'completed') {
        activities.push({
          type: 'tryon',
          title: 'Try-On Completed',
          description: `Customer completed try-on for ${session.productName || 'product'}`,
          timeAgo
        });
      } else if (session.outcome === 'shared') {
        activities.push({
          type: 'user',
          title: 'Try-On Shared',
          description: `Customer shared try-on of ${session.productName || 'product'}`,
          timeAgo
        });
      } else {
        activities.push({
          type: 'tryon',
          title: 'New Try-On Session',
          description: `Customer started try-on for ${session.productName || 'product'}`,
          timeAgo
        });
      }
    });

    res.json(activities);
  } catch (error) {
    console.error('Client recent activity error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});



// PUT /api/analytics/session/:sessionId - Update session (e.g., mark as converted)
router.put('/session/:sessionId', async (req, res) => {
  try {
    const { sessionId } = req.params;
    const updateData = req.body;

    // If endTime is provided but no duration, calculate it
    if (updateData.endTime && !updateData.duration) {
      const session = await TryOnSession.findOne({ sessionId });
      if (session && session.startTime) {
        const endTime = new Date(updateData.endTime);
        const startTime = new Date(session.startTime);
        updateData.duration = Math.floor((endTime - startTime) / 1000);
      }
    }

    // If duration is provided but no endTime, calculate endTime
    if (updateData.duration && !updateData.endTime) {
      const session = await TryOnSession.findOne({ sessionId });
      if (session && session.startTime) {
        const startTime = new Date(session.startTime);
        updateData.endTime = new Date(startTime.getTime() + (updateData.duration * 1000));
      }
    }

    const session = await TryOnSession.findOneAndUpdate(
      { sessionId },
      updateData,
      { new: true }
    );

    if (!session) {
      return res.status(404).json({ message: 'Session not found' });
    }

    // If marking as converted, update product analytics
    if (updateData.converted && updateData.conversionValue) {
      await Product.findOneAndUpdate(
        { productId: session.productId },
        {
          $inc: {
            'analytics.totalConversions': 1,
            'analytics.revenue': updateData.conversionValue
          }
        }
      );
    }

    res.json({ message: 'Session updated', session });
  } catch (error) {
    console.error('Session update error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// GET /api/analytics/admin/time-analysis - Time-based analytics
router.get('/admin/time-analysis', auth, requireAdmin, async (req, res) => {
  try {
    const { start, end } = parseDateRange(req);

    // Get hourly distribution
    const hourlyData = await TryOnSession.aggregate([
      {
        $match: {
          createdAt: { $gte: start, $lte: end }
        }
      },
      {
        $group: {
          _id: { $hour: '$createdAt' },
          sessions: { $sum: 1 },
          conversions: { $sum: { $cond: ['$converted', 1, 0] } }
        }
      },
      { $sort: { _id: 1 } }
    ]);

    // Get daily distribution
    const dailyData = await TryOnSession.aggregate([
      {
        $match: {
          createdAt: { $gte: start, $lte: end }
        }
      },
      {
        $group: {
          _id: { $dayOfWeek: '$createdAt' },
          sessions: { $sum: 1 },
          conversions: { $sum: { $cond: ['$converted', 1, 0] } }
        }
      },
      { $sort: { _id: 1 } }
    ]);

    // Get monthly trends
    const monthlyData = await TryOnSession.aggregate([
      {
        $match: {
          createdAt: { $gte: start, $lte: end }
        }
      },
      {
        $group: {
          _id: {
            year: { $year: '$createdAt' },
            month: { $month: '$createdAt' }
          },
          sessions: { $sum: 1 },
          conversions: { $sum: { $cond: ['$converted', 1, 0] } },
          revenue: { $sum: '$conversionValue' }
        }
      },
      { $sort: { '_id.year': 1, '_id.month': 1 } }
    ]);

    res.json({
      hourly: hourlyData,
      daily: dailyData,
      monthly: monthlyData
    });
  } catch (error) {
    console.error('Time analysis error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// GET /api/analytics/admin/conversion-funnel - Conversion funnel analytics
router.get('/admin/conversion-funnel', auth, requireAdmin, async (req, res) => {
  try {
    const { start, end } = parseDateRange(req);

    const funnelData = await TryOnSession.aggregate([
      {
        $match: {
          createdAt: { $gte: start, $lte: end }
        }
      },
      {
        $group: {
          _id: null,
          totalSessions: { $sum: 1 },
          interactedSessions: { $sum: { $cond: [{ $gt: [{ $size: '$interactions' }, 0] }, 1, 0] } },
          completedSessions: { $sum: { $cond: [{ $eq: ['$outcome', 'completed'] }, 1, 0] } },
          sharedSessions: { $sum: { $cond: [{ $eq: ['$outcome', 'shared'] }, 1, 0] } },
          convertedSessions: { $sum: { $cond: ['$converted', 1, 0] } }
        }
      }
    ]);

    const funnel = funnelData[0] || {};

    res.json({
      steps: [
        {
          name: 'Started Try-On',
          count: funnel.totalSessions || 0,
          percentage: 100
        },
        {
          name: 'Interacted',
          count: funnel.interactedSessions || 0,
          percentage: funnel.totalSessions > 0 ? Math.round((funnel.interactedSessions / funnel.totalSessions) * 100) : 0
        },
        {
          name: 'Completed',
          count: funnel.completedSessions || 0,
          percentage: funnel.totalSessions > 0 ? Math.round((funnel.completedSessions / funnel.totalSessions) * 100) : 0
        },
        {
          name: 'Shared',
          count: funnel.sharedSessions || 0,
          percentage: funnel.totalSessions > 0 ? Math.round((funnel.sharedSessions / funnel.totalSessions) * 100) : 0
        },
        {
          name: 'Converted',
          count: funnel.convertedSessions || 0,
          percentage: funnel.totalSessions > 0 ? Math.round((funnel.convertedSessions / funnel.totalSessions) * 100) : 0
        }
      ]
    });
  } catch (error) {
    console.error('Conversion funnel error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// GET /api/analytics/admin/recent-activity - Recent activity for notifications
router.get('/admin/recent-activity', auth, requireAdmin, async (req, res) => {
  try {
    // Get recent try-on sessions
    const recentSessions = await TryOnSession.find()
      .sort({ createdAt: -1 })
      .limit(10)
      .populate('clientId', 'companyName')
      .select('createdAt converted outcome clientId productId');

    // Get recent client registrations
    const recentClients = await User.find({ role: 'client' })
      .sort({ createdAt: -1 })
      .limit(5)
      .select('companyName createdAt');

    // Format activities
    const activities = [];

    // Add recent sessions
    recentSessions.forEach(session => {
      activities.push({
        type: session.converted ? 'conversion' : 'session',
        title: session.converted ? 'New Conversion' : 'New Try-On Session',
        description: `${session.clientId?.companyName || 'Unknown Client'} - ${session.converted ? 'Converted' : 'Try-on session'}`,
        timestamp: session.createdAt
      });
    });

    // Add recent clients
    recentClients.forEach(client => {
      activities.push({
        type: 'user',
        title: 'New Client Registration',
        description: `${client.companyName} has registered`,
        timestamp: client.createdAt
      });
    });

    // Sort by timestamp and limit
    activities.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
    const limitedActivities = activities.slice(0, 10);

    res.json({
      activities: limitedActivities
    });
  } catch (error) {
    console.error('Recent activity error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// POST /api/analytics/session/:sessionId/performance - Track performance metrics
router.post('/session/:sessionId/performance', async (req, res) => {
  try {
    const { sessionId } = req.params;
    const { metric, value, timestamp } = req.body;

    const session = await TryOnSession.findOne({ sessionId });
    if (!session) {
      return res.status(404).json({ message: 'Session not found' });
    }

    if (metric === 'pageLoadTime') {
      session.performanceMetrics.pageLoadTime = value;
    } else if (metric === 'apiResponseTime') {
      session.performanceMetrics.apiResponseTimes.push({
        endpoint: req.body.endpoint,
        duration: value,
        timestamp: new Date(timestamp)
      });
    } else if (metric === 'frameRate') {
      const data = req.body.data || {};
      session.performanceMetrics.frameRate = {
        average: data.average || value,
        min: data.min || 0,
        max: data.max || 0,
        drops: data.drops || 0
      };
    } else if (metric === 'memoryUsage') {
      const data = req.body.data || {};
      session.performanceMetrics.memoryUsage = {
        used: data.used || value,
        total: data.total || 0,
        timestamp: new Date(timestamp)
      };
    } else if (metric === 'networkMetrics') {
      session.performanceMetrics.networkMetrics = req.body.data || {};
    } else if (metric === 'renderMetrics') {
      session.performanceMetrics.renderMetrics = {
        ...session.performanceMetrics.renderMetrics,
        ...(req.body.data || {})
      };
    } else if (metric === 'resourceLoadTime') {
      const data = req.body.data || {};
      session.performanceMetrics.resourceLoadTimes.push({
        resource: data.resource,
        loadTime: value,
        size: data.size || 0,
        timestamp: new Date(timestamp)
      });
    }

    await session.save();
    res.json({ message: 'Performance metric recorded' });
  } catch (error) {
    console.error('Performance metric recording error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// POST /api/analytics/session/:sessionId/error - Track errors
router.post('/session/:sessionId/error', async (req, res) => {
  try {
    const { sessionId } = req.params;
    const { type, message, timestamp } = req.body;

    const session = await TryOnSession.findOne({ sessionId });
    if (!session) {
      return res.status(404).json({ message: 'Session not found' });
    }

    session.performanceMetrics.errors.push({
      type,
      message,
      timestamp: new Date(timestamp)
    });

    await session.save();
    res.json({ message: 'Error recorded' });
  } catch (error) {
    console.error('Error recording error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// POST /api/analytics/session/:sessionId/behavior - Update behavior metrics
router.post('/session/:sessionId/behavior', async (req, res) => {
  try {
    const { sessionId } = req.params;
    const { metric, value } = req.body;

    const session = await TryOnSession.findOne({ sessionId });
    if (!session) {
      return res.status(404).json({ message: 'Session not found' });
    }

    // Initialize behaviorMetrics if it doesn't exist
    if (!session.behaviorMetrics) {
      session.behaviorMetrics = {
        timeToFirstInteraction: null,
        cameraInitSuccess: false,
        handDetectionSuccess: false,
        backgroundRemovalSuccess: false,
        productSwitches: 0,
        engagementScore: 0,
        scrollDepth: 0,
        mouseMovements: 0,
        featureUsage: {
          cameraToggle: 0,
          productRotation: 0,
          sizeAdjustment: 0,
          colorChange: 0,
          screenshot: 0,
          share: 0,
          zoom: 0
        },
        exitIntent: {
          detected: false,
          timestamp: null,
          beforeConversion: false
        },
        attentionMetrics: {
          focusTime: 0,
          blurEvents: 0,
          returnEvents: 0,
          idleTime: 0
        }
      };
    }

    // Update the specific behavior metric
    if (metric in session.behaviorMetrics) {
      session.behaviorMetrics[metric] = value;
    } else if (metric.includes('.')) {
      // Handle nested properties like 'featureUsage.screenshot'
      const [parent, child] = metric.split('.');
      if (session.behaviorMetrics[parent] && child in session.behaviorMetrics[parent]) {
        session.behaviorMetrics[parent][child] = value;
      }
    }

    await session.save();
    res.json({ message: 'Behavior metric updated' });
  } catch (error) {
    console.error('Behavior metric update error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// GET /api/analytics/admin/behavior - Get behavior analytics
router.get('/admin/behavior', auth, requireAdmin, async (req, res) => {
  try {
    const { start, end } = parseDateRange(req);

    // Get behavior metrics - only from sessions that have behaviorMetrics data
    const behaviorData = await TryOnSession.aggregate([
      {
        $match: {
          createdAt: { $gte: start, $lte: end },
          'behaviorMetrics': { $exists: true }
        }
      },
      {
        $group: {
          _id: null,
          avgTimeToFirstInteraction: {
            $avg: {
              $cond: [
                { $and: [
                  { $ne: ['$behaviorMetrics.timeToFirstInteraction', null] },
                  { $gt: ['$behaviorMetrics.timeToFirstInteraction', 0] }
                ]},
                '$behaviorMetrics.timeToFirstInteraction',
                null
              ]
            }
          },
          cameraInitSuccessRate: {
            $avg: {
              $cond: [
                { $eq: ['$behaviorMetrics.cameraInitSuccess', true] },
                1,
                0
              ]
            }
          },
          handDetectionSuccessRate: {
            $avg: {
              $cond: [
                { $eq: ['$behaviorMetrics.handDetectionSuccess', true] },
                1,
                0
              ]
            }
          },
          backgroundRemovalSuccessRate: {
            $avg: {
              $cond: [
                { $eq: ['$behaviorMetrics.backgroundRemovalSuccess', true] },
                1,
                0
              ]
            }
          },
          avgProductSwitches: {
            $avg: {
              $cond: [
                { $gt: ['$behaviorMetrics.productSwitches', 0] },
                '$behaviorMetrics.productSwitches',
                null
              ]
            }
          }
        }
      }
    ]);

    // Get interaction types distribution - only if interactions exist
    const interactionTypes = await TryOnSession.aggregate([
      {
        $match: {
          createdAt: { $gte: start, $lte: end },
          interactions: { $exists: true, $ne: [] }
        }
      },
      { $unwind: '$interactions' },
      {
        $group: {
          _id: '$interactions.type',
          count: { $sum: 1 }
        }
      },
      {
        $project: {
          name: '$_id',
          count: 1,
          _id: 0
        }
      },
      { $sort: { count: -1 } }
    ]);

    // Get product view times - calculate from session duration by product
    const productViewTimes = await TryOnSession.aggregate([
      {
        $match: {
          createdAt: { $gte: start, $lte: end },
          productName: { $exists: true, $ne: null },
          startTime: { $exists: true, $ne: null },
          updatedAt: { $exists: true, $ne: null }
        }
      },
      {
        $addFields: {
          calculatedDuration: {
            $divide: [{ $subtract: ['$updatedAt', '$startTime'] }, 1000]
          }
        }
      },
      {
        $group: {
          _id: '$productName',
          avgDuration: { $avg: '$calculatedDuration' },
          sessions: { $sum: 1 }
        }
      },
      {
        $project: {
          productName: '$_id',
          avgDuration: { $round: ['$avgDuration', 1] },
          sessions: 1,
          _id: 0
        }
      },
      { $sort: { avgDuration: -1 } },
      { $limit: 10 }
    ]);



    // Get device distribution for behavior analytics
    const deviceStats = await TryOnSession.aggregate([
      {
        $match: {
          createdAt: { $gte: start, $lte: end },
          'device.type': { $exists: true, $ne: null }
        }
      },
      {
        $group: {
          _id: '$device.type',
          count: { $sum: 1 }
        }
      },
      {
        $project: {
          name: {
            $switch: {
              branches: [
                { case: { $eq: ['$_id', 'mobile'] }, then: 'Mobile' },
                { case: { $eq: ['$_id', 'desktop'] }, then: 'Desktop' },
                { case: { $eq: ['$_id', 'tablet'] }, then: 'Tablet' }
              ],
              default: { $toUpper: { $substr: ['$_id', 0, 1] } }
            }
          },
          value: '$count',
          _id: 0
        }
      },
      { $sort: { value: -1 } }
    ]);

    // Get browser distribution
    const browserStats = await TryOnSession.aggregate([
      {
        $match: {
          createdAt: { $gte: start, $lte: end },
          'device.browser': { $exists: true, $ne: null }
        }
      },
      {
        $group: {
          _id: '$device.browser',
          sessions: { $sum: 1 }
        }
      },
      {
        $project: {
          name: '$_id',
          sessions: 1,
          _id: 0
        }
      },
      { $sort: { sessions: -1 } },
      { $limit: 10 }
    ]);

    res.json({
      ...behaviorData[0],
      interactionTypes,
      productViewTimes,
      deviceStats,
      browserStats
    });
  } catch (error) {
    console.error('Behavior analytics error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});



// POST /api/analytics/session/:sessionId/quality - Track quality metrics
router.post('/session/:sessionId/quality', async (req, res) => {
  try {
    const { sessionId } = req.params;
    const { metric, value, data } = req.body;

    const session = await TryOnSession.findOne({ sessionId });
    if (!session) {
      return res.status(404).json({ message: 'Session not found' });
    }

    if (!session.qualityMetrics) {
      session.qualityMetrics = {};
    }

    if (metric === 'handDetectionAccuracy') {
      session.qualityMetrics.handDetectionAccuracy = value;
    } else if (metric === 'backgroundRemovalQuality') {
      session.qualityMetrics.backgroundRemovalQuality = value;
    } else if (metric === 'productFitAccuracy') {
      session.qualityMetrics.productFitAccuracy = value;
    } else if (metric === 'userSatisfactionScore') {
      session.qualityMetrics.userSatisfactionScore = value;
    } else if (metric === 'technicalIssue') {
      if (!session.qualityMetrics.technicalIssues) {
        session.qualityMetrics.technicalIssues = [];
      }
      session.qualityMetrics.technicalIssues.push({
        type: data.type,
        severity: data.severity,
        timestamp: new Date(),
        resolved: false
      });
    }

    await session.save();
    res.json({ message: 'Quality metric recorded' });
  } catch (error) {
    console.error('Quality metric recording error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// POST /api/analytics/session/:sessionId/conversion-funnel - Update conversion funnel
router.post('/session/:sessionId/conversion-funnel', async (req, res) => {
  try {
    const { sessionId } = req.params;
    const { step, value } = req.body;

    const session = await TryOnSession.findOne({ sessionId });
    if (!session) {
      return res.status(404).json({ message: 'Session not found' });
    }

    if (!session.conversionFunnel) {
      session.conversionFunnel = {};
    }

    session.conversionFunnel[step] = value;

    // Mark as converted if completed purchase
    if (step === 'completedPurchase' && value === true) {
      session.converted = true;
      session.conversionTime = new Date();
    }

    await session.save();
    res.json({ message: 'Conversion funnel updated' });
  } catch (error) {
    console.error('Conversion funnel update error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// GET /api/analytics/admin/enhanced-overview - Enhanced overview with new metrics
router.get('/admin/enhanced-overview', auth, requireAdmin, async (req, res) => {
  try {
    const { start, end } = parseDateRange(req);

    // Get enhanced analytics
    const enhancedStats = await TryOnSession.aggregate([
      {
        $match: {
          createdAt: { $gte: start, $lte: end }
        }
      },
      {
        $group: {
          _id: null,
          totalSessions: { $sum: 1 },
          avgEngagementScore: { $avg: '$behaviorMetrics.engagementScore' },
          avgScrollDepth: { $avg: '$behaviorMetrics.scrollDepth' },
          totalMouseMovements: { $sum: '$behaviorMetrics.mouseMovements' },
          avgHandDetectionAccuracy: { $avg: '$qualityMetrics.handDetectionAccuracy' },
          avgBackgroundRemovalQuality: { $avg: '$qualityMetrics.backgroundRemovalQuality' },
          totalScreenshots: { $sum: '$behaviorMetrics.featureUsage.screenshot' },
          totalShares: { $sum: '$behaviorMetrics.featureUsage.share' },
          exitIntentDetected: { $sum: { $cond: ['$behaviorMetrics.exitIntent.detected', 1, 0] } },
          avgFocusTime: { $avg: '$behaviorMetrics.attentionMetrics.focusTime' },
          avgMemoryUsage: { $avg: '$performanceMetrics.memoryUsage.used' },
          avgFrameRate: { $avg: '$performanceMetrics.frameRate.average' }
        }
      }
    ]);

    // Get conversion funnel stats
    const funnelStats = await TryOnSession.aggregate([
      {
        $match: {
          createdAt: { $gte: start, $lte: end }
        }
      },
      {
        $group: {
          _id: null,
          viewedProduct: { $sum: { $cond: ['$conversionFunnel.viewedProduct', 1, 0] } },
          initiatedTryOn: { $sum: { $cond: ['$conversionFunnel.initiatedTryOn', 1, 0] } },
          completedTryOn: { $sum: { $cond: ['$conversionFunnel.completedTryOn', 1, 0] } },
          sharedResult: { $sum: { $cond: ['$conversionFunnel.sharedResult', 1, 0] } },
          addedToCart: { $sum: { $cond: ['$conversionFunnel.addedToCart', 1, 0] } },
          proceededToCheckout: { $sum: { $cond: ['$conversionFunnel.proceededToCheckout', 1, 0] } },
          completedPurchase: { $sum: { $cond: ['$conversionFunnel.completedPurchase', 1, 0] } }
        }
      }
    ]);

    const stats = enhancedStats[0] || {};
    const funnel = funnelStats[0] || {};

    res.json({
      ...stats,
      conversionFunnel: funnel,
      successRate: stats.totalSessions > 0 ? (stats.totalScreenshots / stats.totalSessions) * 100 : 0,
      shareRate: stats.totalSessions > 0 ? (stats.totalShares / stats.totalSessions) * 100 : 0,
      exitIntentRate: stats.totalSessions > 0 ? (stats.exitIntentDetected / stats.totalSessions) * 100 : 0
    });
  } catch (error) {
    console.error('Enhanced overview analytics error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

module.exports = router;
